{"cmd": {"247": {"description": "Botun ses kanalında kalmasını sağlar.", "errors": {"not_in_voice": "Bu komutu kullanmak için bir ses kanalında olmanız gerekiyor.", "generic": "Bu komutu çalıştırmaya çalışırken bir hata oluştu."}, "messages": {"disabled": "`✅` | 24/7 modu `KAPATILDI`.", "enabled": "`✅` | 24/7 modu `AKTİFLEŞTİRİLDİ`."}}, "ping": {"description": "Botun gecikmesini gösterir.", "content": "Pingleniyor...", "bot_latency": "<PERSON><PERSON>", "api_latency": "API Gecikmesi", "requested_by": "{author} ta<PERSON><PERSON><PERSON><PERSON><PERSON> talep edildi."}, "lavalink": {"description": "Mevcut <PERSON> istatistiklerini gösterir.", "title": "Lavalink İstatistikleri", "content": "Oynatıcı: {players}\n<PERSON><PERSON><PERSON>: {playingPlayers}\nÇalışma Süresi: {uptime}\nÇekirdekler: {cores} Çekirdek(ler)\nBellek Kullanımı: {used} / {reservable}\nSistem Yükü: {systemLoad}%\nLavalink Yükü: {lavalinkLoad}%", "page_info": "Sayfa {index} / {total}"}, "invite": {"description": "<PERSON><PERSON> davet bağlantısını al.", "content": "Beni davet etmek için aşağıdaki butona tıklayın. <PERSON>hangi bir hata veya kesinti mi var? Destek sunucusuna katılın!"}, "help": {"description": "<PERSON><PERSON>m menüsünü gösterir.", "options": {"command": "Hakkında bilgi almak istediğiniz komut."}, "content": "Merhab<PERSON>! Ben {bot}, [Lavamusic](https://github.com/appujet/lavamusic) ve Discord.js ile yapılmış bir müzik botuyum. {prefix}help <command> yazarak bir komut hakkında daha fazla bilgi alabilirsiniz.", "title": "<PERSON><PERSON>m <PERSON>", "not_found": "Bu {cmdName} komutu mevcut değil.", "help_cmd": "**Açıklama**: {description}\n**Kullanım**: {usage}\n**Örnekler**: {examples}\n**Alternatifler**: {aliases}\n**Kategori**: {category}\n**Bekleme Süresi**: {cooldown} saniye\n**İzinler**: {premUser}\n**Bot İzinleri**: {premBot}\n**Sadece Geliştirici**: {dev}\n**Slash Komut**: {slash}\n**Argümanlar**: {args}\n**Oynatıcı**: {player}\n**DJ**: {dj}\n**DJ <PERSON>**: {djPerm}\n**Ses**: {voice}", "footer": "Bir komut hakkında daha fazla bilgi almak için {prefix}help <command> kull<PERSON><PERSON>n."}, "botinfo": {"description": "Bot hakkında bilgi verir.", "content": "Bot Bilgisi:\n- **<PERSON><PERSON>let<PERSON>**: {osInfo}\n- **Çalışma Süresi**: {osUptime}\n- **Ana Bilgisayar Adı**: {osHostname}\n- **CPU Mimarisi**: {cpuInfo}\n- **CPU Kullanımı**: %{cpuUsed}\n- **Bellek <PERSON>llanımı**: {memUsed}MB / {memTotal}GB\n- **Node Versiyonu**: {nodeVersion}\n- **Discord.js Versiyonu**: {discordJsVersion}\n- {guilds} sunucuya, {channels} kanala ve {users} kullanıcıya bağlı\n- **Toplam Komutlar**: {commands}"}, "about": {"description": "Bot hakkında bilgi gösterir.", "fields": {"creator": "Oluşturucu", "repository": "<PERSON><PERSON>", "support": "Destek", "description": "İlk açık kaynak projesini yapmak için gerçekten hevesliydi ve daha fazla kodlama deneyimi kazanmak istedi. Bu projede daha az hatalı bir proje oluşturma zorluğuyla karşılaştı. LavaMusic'i kullanmaktan keyif almanızı umuyorum!"}}, "dj": {"description": "DJ modunu ve ilişkili rolleri yönetir.", "errors": {"provide_role": "Lütfen bir rol belirtin.", "no_roles": "DJ r<PERSON><PERSON> yok.", "invalid_subcommand": "Lütfen geçerli bir alt komut belirtin."}, "messages": {"role_exists": "DJ r<PERSON><PERSON> <@&{roleId}> zaten e<PERSON>.", "role_added": "DJ rol<PERSON> <@&{roleId}> eklendi.", "role_not_found": "DJ r<PERSON><PERSON> <@&{roleId}> eklenmemiş.", "role_removed": "DJ rolü <@&{roleId}> kaldırıld<PERSON>.", "all_roles_cleared": "Tüm DJ <PERSON><PERSON> kald<PERSON>.", "toggle": "DJ modu {status} o<PERSON><PERSON>."}, "options": {"add": "Eklemek istediğiniz DJ rolü", "remove": "Kaldırmak istediğiniz DJ rolü.", "clear": "<PERSON>üm DJ <PERSON><PERSON> temizler.", "toggle": "DJ <PERSON><PERSON><PERSON><PERSON><PERSON>.", "role": "DJ r<PERSON><PERSON>."}, "subcommands": "Alt komutlar"}, "language": {"description": "<PERSON><PERSON>.", "invalid_language": "Lütfen geçerli bir dil belirtin. Örnek: `EnglishUS` İngilizce (ABD) için.\n\nDesteklenen dillerin listesine [buradan](https://discord.com/developers/docs/reference#locales) ulaşabilirsiniz.\n\nMevcut Diller:\n{languages}", "already_set": "Dil zaten `{language}` o<PERSON>ak ayarlanmış.", "not_set": "<PERSON><PERSON>ı<PERSON>.", "set": "`✅` | Dil `{language}` olarak ayarlandı.", "reset": "`✅` | <PERSON><PERSON>ılan dile sıfırlandı.", "options": {"set": "<PERSON><PERSON>.", "language": "Ayarlamak istediğiniz dil.", "reset": "<PERSON><PERSON> o<PERSON>."}}, "prefix": {"description": "Botun ön ekini gö<PERSON>ir veya a<PERSON>.", "errors": {"prefix_too_long": "Ön ek 3 karakterden uzun olamaz."}, "messages": {"current_prefix": "<PERSON>u sunucu i<PERSON>in ön ek `{prefix}`.", "prefix_set": "<PERSON>u sunucu için ön ek şimdi `{prefix}`.", "prefix_reset": "<PERSON>u sunucu için ön ek şimdi `{prefix}`."}, "options": {"set": "<PERSON>n <PERSON> a<PERSON>.", "prefix": "Ayarlamak istediğiniz ön ek.", "reset": "Ön <PERSON> var<PERSON>ılana s<PERSON>."}}, "setup": {"description": "<PERSON><PERSON> k<PERSON>", "errors": {"channel_exists": "Şarkı isteği kanalı zaten mevcut.", "channel_not_exists": "Şarkı isteği kanalı mevcut değil", "channel_delete_fail": "Kurulum kanalı veritabanından silindi. Lütfen kanalı kendiniz silin."}, "messages": {"channel_created": "Şarkı isteği kanalı <#{channelId}> kanalında oluşturuldu.", "channel_deleted": "Şarkı isteği kanalı silindi.", "channel_info": "Şarkı isteği kanalı <#{channelId}>."}, "options": {"create": "Şarkı isteği kanalı oluşturur.", "delete": "Şarkı isteği kanalını siler.", "info": "Şarkı isteği kanalını gösterir."}}, "8d": {"description": "8D filtresini açar/kapatır.", "messages": {"filter_enabled": "`✅` | 8D filtresi `AÇILDI`.", "filter_disabled": "`✅` | 8D filtresi `KAPATILDI`."}}, "bassboost": {"description": "Bassboost filtresini açar/kapatır.", "options": {"level": "<PERSON><PERSON><PERSON><PERSON> istediğiniz bassboost se<PERSON><PERSON><PERSON>."}, "messages": {"high": "`✅` | Yüksek bassboost filtresi `AÇILDI`.", "low": "`✅` | Düşük bassboost filtresi `AÇILDI`.", "medium": "`✅` | Orta bassboost filtresi `AÇILDI`.", "off": "`✅` | Bassboost filtresi `KAPATILDI`."}}, "distortion": {"description": "Distortion filtresini açar/kapatır.", "messages": {"filter_enabled": "`✅` | Distortion filtresi `AÇILDI`.", "filter_disabled": "`✅` | Distortion filtresi `KAPATILDI`."}}, "karaoke": {"description": "Karaoke filtresini açar/kapatır.", "messages": {"filter_enabled": "`✅` | Karaoke filtresi `AÇILDI`.", "filter_disabled": "`✅` | Karaoke filtresi `KAPATILDI`."}}, "lowpass": {"description": "Lowpass filtresini açar/kapatır.", "messages": {"filter_enabled": "`✅` | Lowpass filtresi `AÇILDI`.", "filter_disabled": "`✅` | Lowpass filtresi `KAPATILDI`."}}, "nightcore": {"description": "Nightcore filtresini açar/kapatır.", "messages": {"filter_enabled": "`✅` | Nightcore filtresi `AÇILDI`.", "filter_disabled": "`✅` | Nightcore filtresi `KAPATILDI`."}}, "pitch": {"description": "Pitch filtresini açar/kapatır.", "options": {"pitch": "Ayarlamak istediğiniz pitch değeri (0.5 ile 5 arasında)"}, "errors": {"invalid_number": "Lütfen 0.5 ile 5 arasında geçerli bir sayı girin."}, "messages": {"pitch_set": "`✅` | Pitch **{pitch}** olarak ayarlandı."}}, "rate": {"description": "Şarkının hızını değiştirir.", "options": {"rate": "Ayarlamak istediğiniz hız <PERSON> (0.5 ile 5 arasında)"}, "errors": {"invalid_number": "Lütfen 0.5 ile 5 arasında geçerli bir sayı girin."}, "messages": {"rate_set": "`✅` | Hız **{rate}** olarak ayarlandı.."}}, "reset": {"description": "Aktif filt<PERSON> sıfırlar", "messages": {"filters_reset": "`✅` | Filtreler sıfırlandı."}}, "rotation": {"description": "Rotation filtresini açar/kapatır.", "messages": {"enabled": "`✅` | Rotation filtresi `AÇILDI`.", "disabled": "`✅` | Rotation filtresi `KAPATILDI`."}}, "speed": {"description": "Şarkının hızını değiştirir.", "options": {"speed": "Ayarlamak istediğiniz hız."}, "messages": {"invalid_number": "Lütfen 0.5 ile 5 arasında geçerli bir sayı girin.", "set_speed": "`✅` | Hız **{speed}** olarak ayarlandı."}}, "tremolo": {"description": "Tremolo filtresini açar/kapatır.", "messages": {"enabled": "`✅` | Tremolo filtresi `AÇILDI`.", "disabled": "`✅` | Tremolo filtresi `KAPATILDI`."}}, "vibrato": {"description": "Vibrato filtresini açar/kapatır.", "messages": {"enabled": "`✅` | Vibrato filtresi `AÇILDI`.", "disabled": "`✅` | Vibrato filtresi `KAPATILDI`."}}, "autoplay": {"description": "Otomatik çalmayı açar/kapatır.", "messages": {"enabled": "`✅` | Otomatik çalma `AÇILDI`.", "disabled": "`✅` | Otomatik çalma `KAPATILDI`."}}, "clearqueue": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "messages": {"cleared": "Kuyruk temizlendi."}}, "grab": {"description": "Çalan şarkıyı özel mesaj yoluyla gönderir.", "loading": "Yükleniyor...", "content": "**Süre**: {length}\n**Talep Eden**: <@{requester}>\n**Bağlantı**: [<PERSON><PERSON><PERSON> tıklayın]({uri})", "check_dm": "Lütfen özel mesajlarınızı kontrol edin.", "dm_failed": "Özel mesaj gönderemedim. Lütfen doğrudan mesajların açık olduğundan emin olun."}, "join": {"description": "Botun ses kanalına katılmasını sağlar.", "already_connected": "Zaten <#{channelId}> kanalına bağlıyım.", "no_voice_channel": "Bu komutu kullanmak için bir ses kanalında olmanız gerekiyor.", "joined": "<#{channelId}> kanalına başarıyla katıldı."}, "leave": {"description": "<PERSON>tu ses kanalından ç<PERSON>ı<PERSON>.", "left": "<#{channelId}> kanalından başarıyla ayrıldı.", "not_in_channel": "Bir ses kanalında <PERSON>."}, "loop": {"description": "Mevcut şarkıyı veya sırayı döngüye alır.", "looping_song": "**Şarkı döngüde.**", "looping_queue": "**<PERSON><PERSON><PERSON> dö<PERSON>.**", "looping_off": "**Döngü kapatıldı.**"}, "lyrics": {"description": "Şu an çalan parçanın sözlerini alır.", "lyrics_track": "### S<PERSON>zler: [{trackTitle}]({trackUrl})\n**`{lyrics}`**", "searching": "`🔍` {trackTitle} şarkı sözleri aranıyor...", "errors": {"no_results": "Mevcut parça için şarkı sözleri bulunamadı.", "lyrics_error": "Şarkı sözlerini alırken bir hata oluştu."}}, "nowplaying": {"description": "Şu an çalan şarkıyı gösterir.", "now_playing": "Şu An Çalıyor", "track_info": "[{title}]({uri})- <PERSON><PERSON><PERSON><PERSON>: <@{requester}>\n\n{bar}`"}, "pause": {"description": "Mevcut şarkıyı duraklatır.", "successfully_paused": "Şarkı başarıyla duraklatıldı."}, "play": {"description": "YouTube, Spotify veya bir bağlantıdan şarkı çalar.", "options": {"song": "Çalmak istediğiniz şarkı."}, "loading": "Yükleniyor...", "errors": {"search_error": "<PERSON><PERSON> sı<PERSON>nda bir hata oluş<PERSON>.", "no_results": "<PERSON><PERSON><PERSON> bulunamadı.", "queue_too_long": "Kuyruk çok uzun. Maksimum uzunluk {maxQueueSize} şarkıdır.", "playlist_too_long": "Çalma listesi çok uzun. Maksimum uzunluk {maxPlaylistSize} şarkıdır."}, "added_to_queue": "<PERSON><PERSON><PERSON><PERSON><PERSON> [{title}]({uri}) eklendi.", "added_playlist_to_queue": "{length} şarkı kuyruğa eklendi."}, "playnext": {"description": "Sıradaki olarak çalınacak şarkıyı kuyruğa ekler.", "options": {"song": "Sıradaki olarak çalmak istediğiniz şarkı."}, "loading": "Yükleniyor...", "errors": {"search_error": "<PERSON><PERSON> sı<PERSON>nda bir hata oluş<PERSON>.", "no_results": "<PERSON><PERSON><PERSON> bulunamadı.", "queue_too_long": "Kuyruk çok uzun. Maksimum uzunluk {maxQueueSize} şarkıdır.", "playlist_too_long": "Çalma listesi çok uzun. Maksimum uzunluk {maxPlaylistSize} şarkıdır."}, "added_to_play_next": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> [{title}]({uri}) kuyruğa eklendi.", "added_playlist_to_play_next": "{length} şarkı sıradaki olarak kuyruğa eklendi."}, "queue": {"description": "Mevcut kuyruğu gösterir.", "now_playing": "[{title}]({uri}) - <PERSON><PERSON><PERSON><PERSON>: <@{requester}> - <PERSON><PERSON><PERSON>: `{duration}`", "live": "Canlı Yayın", "track_info": "{index}. [{title}]({uri}) - <PERSON><PERSON><PERSON><PERSON>: <@{requester}> - <PERSON><PERSON><PERSON>: `{duration}`", "title": "Kuyruk", "page_info": "Sayfa {index} / {total}"}, "remove": {"description": "Kuyruktan bir şarkıyı kaldırır.", "options": {"song": "Kaldırmak istediğiniz şarkı numarası."}, "errors": {"no_songs": "Kuyrukta şarkı yok.", "invalid_number": "Lütfen geçerli bir şarkı numarası girin."}, "messages": {"removed": "{songNumber} numaralı şarkı kuyruktan kaldırıldı."}}, "replay": {"description": "Mevcut parçayı tekrar çalar.", "errors": {"not_seekable": "Bu parça tekrarlanamaz çünkü ileri/geri sarılamaz."}, "messages": {"replaying": "Mevcut parça tekrarlanıyor."}}, "resume": {"description": "Mevcut şarkıyı devam ettirir.", "errors": {"not_paused": "Oynatıcı duraklatılmamış."}, "messages": {"resumed": "Oynatıcı devam ettirildi."}}, "search": {"description": "Şarkı arar.", "options": {"song": "Aramak istediğiniz şarkı."}, "errors": {"no_results": "<PERSON><PERSON><PERSON> bulunamadı.", "search_error": "<PERSON><PERSON> sı<PERSON>nda bir hata oluş<PERSON>."}, "messages": {"added_to_queue": "<PERSON><PERSON><PERSON><PERSON><PERSON> {title}]({uri}) eklendi."}}, "seek": {"description": "Şarkının belirli bir süresine gider.", "options": {"duration": "Gitmek istediğiniz süre."}, "errors": {"invalid_format": "Geçersiz zaman formatı. Örnekler: seek 1m, seek 1h 30m", "not_seekable": "<PERSON>u parça ileri/geri sarı<PERSON>az", "beyond_duration": "Şarkının süresi olan {length} süresini aşamazsınız."}, "messages": {"seeked_to": "{duration} s<PERSON><PERSON><PERSON> gidildi."}}, "shuffle": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> karı<PERSON>tırı<PERSON>.", "messages": {"shuffled": "Kuyruk karıştırıldı."}}, "skip": {"description": "Mevcut şarkıyı geçer.", "messages": {"skipped": "[{title}]({uri}) şarkısı geçildi.."}}, "skipto": {"description": "Kuyrukta belirli bir şarkıya atlar.", "options": {"number": "Atlamak istediğiniz şarkının numarası."}, "errors": {"invalid_number": "Lütfen geçerli bir numara girin."}, "messages": {"skipped_to": "{number} numaralı şarkıya atlandı."}}, "stop": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> durdurur ve kuyruğu temizler.", "messages": {"stopped": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>u ve kuyruk temizlendi."}}, "volume": {"description": "Oynatıcının ses seviyesini ayarlar.", "options": {"number": "Ayarlamak istediğiniz ses seviyesi."}, "messages": {"invalid_number": "Lütfen geçerli bir sayı girin.", "too_low": "Ses seviyesi 0'dan düşük olamaz.", "too_high": "Ses seviyesi 200'den yüksek olamaz. İşitme veya hoparlörlerinize zarar vermek istemezsiniz, değil mi?", "set": "Ses seviyesi {volume} olarak ayarlandı."}}, "addsong": {"description": "Çalma listesine bir şarkı ekler.", "options": {"playlist": "Eklemek istediğiniz çalma listesi.", "song": "Eklemek istediğiniz şarkı."}, "messages": {"no_playlist": "Lütfen bir çalma listesi belirtin.", "no_song": "Lütfen bir şarkı belirtin.", "playlist_not_found": "<PERSON>u çalma listesi mevcut değil.", "no_songs_found": "Şarkı bulunamadı.", "added": "{count} şarkı {playlist} çalma listesine eklendi."}}, "create": {"description": "Bir çalma listesi oluşturur.", "options": {"name": "Çalma listesinin adı."}, "messages": {"name_too_long": "Çalma listesi adları en fazla 50 karakter uzunluğunda olabilir.", "playlist_exists": "Bu isimde bir çalma listesi zaten var. Lütfen farklı bir isim kullanın.", "playlist_created": "<PERSON><PERSON><PERSON> listesi **{name}** o<PERSON><PERSON><PERSON><PERSON><PERSON>."}}, "delete": {"description": "Bir çalma <PERSON> si<PERSON>.", "options": {"playlist": "<PERSON>lmek istediğiniz çalma <PERSON>."}, "messages": {"playlist_not_found": "<PERSON>u çalma listesi mevcut değil.", "playlist_deleted": "<PERSON><PERSON><PERSON> listesi **{playlistName}** si<PERSON><PERSON>."}}, "list": {"description": "Kullanıcının tüm çalma listelerini getirir.", "options": {"user": "Çalma listelerini görmek istediğiniz kullanıcı."}, "messages": {"no_playlists": "Bu kullanıcının çalma listesi yok.", "your": "<PERSON><PERSON>", "playlists_title": "{username} ad<PERSON><PERSON> kullanıcının Çalma Listeleri", "error": "Çalma listeleri alınırken bir hata oluştu."}}, "load": {"description": "Bir çalma <PERSON>.", "options": {"playlist": "Yüklemek istediğiniz çalma listesi."}, "messages": {"playlist_not_exist": "<PERSON>u çalma listesi mevcut değil.", "playlist_empty": "<PERSON><PERSON> <PERSON><PERSON><PERSON> listesi boş.", "playlist_loaded": "`{name}` <PERSON><PERSON><PERSON><PERSON>, `{count} şark<PERSON> içeriyor."}}, "removesong": {"description": "Çalma listesinden bir şarkıyı kaldırır.", "options": {"playlist": "Kaldırmak istediğiniz çalma listesi.", "song": "Kaldırmak istediğiniz şarkı."}, "messages": {"provide_playlist": "Lütfen bir çalma listesi belirtin.", "provide_song": "Lütfen bir şarkı belirtin.", "playlist_not_exist": "<PERSON>u çalma listesi mevcut değil.", "song_not_found": "Eşleşen şarkı bulunamadı.", "song_removed": "{playlist} çal<PERSON> listesinden {song} kaldırıldı.", "error_occurred": "Şarkı kaldırılırken bir hata oluştu."}}, "steal": {"description": "Başka bir kullanıcıdan bir çalma listesini alır ve kendi çalma listelerinize ekler.", "options": {"playlist": "Almak istediğiniz çalma listesi.", "user": "Çalma listesini almak istediğiniz kullanıcı."}, "messages": {"provide_playlist": "Lütfen bir çalma listesi adı belirtin", "provide_user": "Lütfen bir kullanıcı etiketleyin.", "playlist_not_exist": "Bu kullanıcı için belirtilen çalma listesi mevcut değil.", "playlist_stolen": "`{playlist}` adlı çalma listesi {user} adlı kullanı<PERSON>ıdan başarıyla alındı.", "error_occurred": "Çalma listesi alınırken bir hata oluştu."}}, "fairplay": {"description": "Set the bot to play music fairly"}, "playlocal": {"description": "Plays an your local audio files", "options": {"file": "The audio file you want to play"}}, "say": {"description": "Quick text to speech conversion", "options": {"text": "The text to say"}}, "speak": {"description": "Convert text to speech with advanced voice options", "options": {"text": "The text to convert to speech", "voice": "The voice to use for speech synthesis", "speed": "The speed of speech (0.5 to 2.0)"}}, "mluvit": {"description": "Czech text to speech conversion", "options": {"text": "Text to convert to Czech speech", "voice": "Czech voice to use (<PERSON><PERSON><PERSON> or <PERSON><PERSON>)", "speed": "Speech speed (0.5 to 2.0)"}}, "developer": {"description": "Shows information about the bot developer"}}, "buttons": {"invite": "<PERSON><PERSON>", "support": "Destek <PERSON>", "previous": "<PERSON><PERSON><PERSON>", "resume": "<PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON>", "skip": "Geç", "loop": "Tekrarla", "errors": {"not_author": "<PERSON>u butonu kullanamazsınız."}}, "player": {"errors": {"no_player": "Bu sunucuda aktif bir oynatıcı yok.", "no_channel": "Bu komutu kullanmak için bir ses kanalında olmanız gerekiyor.", "queue_empty": "Kuyruk boş.", "no_previous": "Kuyrukta önceki şarkı yok.", "no_song": "Kuyrukta şarkı yok.", "already_paused": "Şarkı zaten duraklatılmış durumda."}, "trackStart": {"now_playing": "Şu An Çalıyor", "requested_by": "İsteyen: {user}", "duration": "<PERSON><PERSON><PERSON>", "author": "<PERSON><PERSON>", "not_connected_to_voice_channel": "Bu butonları kullanmak için <#{channel}> kanalına bağlı değilsiniz.", "need_dj_role": "Bu komutu kullanmak için DJ rolüne sahip olmalısınız.", "previous_by": "Önce<PERSON>: {user}", "no_previous_song": "Önceki şarkı yok.", "paused_by": "Durak<PERSON>ıldı: {user}", "resumed_by": "<PERSON><PERSON>: {user}", "skipped_by": "Geçildi: {user}", "no_more_songs_in_queue": "Kuyrukta başka şarkı yok.", "looping_by": "Tekrar: {user}", "looping_queue_by": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {user}", "looping_off_by": "<PERSON><PERSON><PERSON>: {user}"}, "setupStart": {"now_playing": "Şu An Çalıyor", "description": "[{title}]({uri}) tarafından {author} • `[{length}]` - <PERSON><PERSON><PERSON><PERSON> <@{requester}>", "error_searching": "<PERSON><PERSON> sı<PERSON>nda bir hata oluş<PERSON>.", "no_results": "<PERSON><PERSON><PERSON> bulunamadı.", "nothing_playing": "<PERSON><PERSON> anda hiçbir şey çalmıyor.", "queue_too_long": "Kuyruk çok uzun. Maksimum uzunluk {maxQueueSize} şarkıdır.", "playlist_too_long": "Çalma listesi çok uzun. Maksimum uzunluk {maxPlaylistSize} şarkıdır.", "added_to_queue": "<PERSON><PERSON><PERSON><PERSON><PERSON> [{title}]({uri}) eklendi.", "added_playlist_to_queue": "Kuyruğa [{length}] şarkı eklendi."}}, "event": {"interaction": {"setup_channel": "Bu komutu kurulum kanalında kullanamazsınız.", "no_send_message": "**`<PERSON><PERSON>`**, **`Kanalı Gör`**, **`<PERSON><PERSON>`** veya **`<PERSON>j Geçmişini Oku`** iznim yok.", "no_permission": "{permissions} iznim yok.", "no_user_permission": "Bu komutu kullanma izniniz yok.", "no_voice_channel": "`{command}` komutunu kullanmak için bir ses kanalına bağlı olmalısınız.", "no_connect_permission": "{command} komutunu kullan<PERSON>k i<PERSON>in `BAĞLAN` iznim yok.", "no_speak_permission": "{command} komutunu kullanmak için `KONUŞ` iznim yok.", "no_request_to_speak": "{command} komutunu kullan<PERSON>k i<PERSON>in `KONUŞMAYI TALEP ETME` iznim yok.", "different_voice_channel": "`{command}` komutunu kullanmak için {channel} kanalında bağlı değilsiniz.", "no_music_playing": "<PERSON><PERSON> anda çalan bir şey yok.", "no_dj_role": "DJ <PERSON><PERSON><PERSON>.", "no_dj_permission": "Bu komutu kullanmak için DJ rolüne sahip olmalısınız.", "cooldown": "`{command}` komutunu yeniden kullanmadan önce {time} saniye bekleyin.", "error": "<PERSON>ir hata o<PERSON>: `{error}`", "vote_button": "Oy Ver!", "vote_message": "Durun! Bu komutu kullanabilmek için top.gg üzerinde oy vermelisiniz. Teşekkürler."}, "message": {"prefix_mention": "<PERSON><PERSON><PERSON><PERSON>, bu sunucu için ön ekim `{prefix}`. <PERSON>ha fazla bilgi mi istiyorsunuz? `{prefix}help` yapın\n<PERSON> kalın, ha<PERSON> kalın!", "no_send_message": "**`<PERSON><PERSON>`**, **`Kanalı Gör`**, **`<PERSON><PERSON>`** veya **`<PERSON>j Geçmişini Oku`** iznim yok.", "no_permission": "{permissions} iznim yok.", "no_user_permission": "Bu komutu kullanma izniniz yok.", "no_voice_channel": "`{command}` komutunu kullanmak için bir ses kanalına bağlı olmalısınız.", "no_connect_permission": "{command} komutunu kullan<PERSON>k i<PERSON>in `BAĞLAN` iznim yok.", "no_speak_permission": "{command} komutunu kullanmak için `KONUŞ` iznim yok.", "no_request_to_speak": "{command} komutunu kullan<PERSON>k i<PERSON>in `KONUŞMAYI TALEP ETME` iznim yok.", "different_voice_channel": "`{command}` komutunu kullanmak için {channel} kanalında bağlı değilsiniz.", "no_music_playing": "<PERSON><PERSON> anda çalan bir şey yok.", "no_dj_role": "DJ <PERSON><PERSON><PERSON>.", "no_dj_permission": "Bu komutu kullanmak için DJ rolüne sahip olmalısınız.", "missing_arguments": "Missing Arguments", "missing_arguments_description": "`{command}` komutu için gerekli argümanları belirtin.\n\nÖrnekler:\n{examples}", "syntax_footer": "Sözdizimi: [] = isteğe bağlı, <> = zorunlu", "cooldown": "`{command}` komutunu yeniden kullanmadan önce {time} saniye bekleyin.", "no_mention_everyone": "Everyone veya here kull<PERSON><PERSON>mıyla bu komutu kullanamazsınız. Lütfen slash komutunu kullanın.", "error": "<PERSON>ir hata o<PERSON>: `{error}`", "no_voice_channel_queue": "Şarkıları kuyruğa almak için bir ses kanalına bağlı olmalısınız.", "no_permission_connect_speak": "<#{channel}> kanalına bağlanma/konuşma iznim yok.", "different_voice_channel_queue": "Şarkıları kuyruğa almak için <#{channel}> kanalına bağlı olmalısınız.", "vote_button": "Oy Ver!", "vote_message": "Durun! Bu komutu kullanabilmek için top.gg üzerinde oy vermelisiniz. Teşekkürler."}, "setupButton": {"no_voice_channel_button": "Bu butonu kullanmak için bir ses kanalına bağlı olmalısınız.", "different_voice_channel_button": "Bu butonları kullanmak için {channel} kanalında bağlı olmalısınız.", "now_playing": "Şu An Çalıyor", "live": "CANLI", "requested_by": "İsteyen: <@{requester}>", "no_dj_permission": "<PERSON>u butonu kullanmak için DJ rolüne sahip olmalısınız.", "volume_set": "Ses seviyesi %{vol} olarak ayarlandı.", "volume_footer": "Ses: %{vol}", "paused": "Duraklatıldı", "resumed": "<PERSON><PERSON>", "pause_resume": "{name} the music.", "pause_resume_footer": "{displayName} tarafından {name}.", "no_music_to_skip": "Geçilecek müzik yok.", "skipped": "Müzik geçildi.", "skipped_footer": "{displayName} tarafından geçildi.", "stopped": "<PERSON><PERSON><PERSON><PERSON>.", "stopped_footer": "{displayName} tara<PERSON>ından durduruldu.", "nothing_playing": "<PERSON><PERSON> anda hiçbir şey çalmıyor.", "loop_set": "<PERSON><PERSON>r a<PERSON>landı: {loop}.", "loop_footer": "{displayName} tarafından tekrar ayarlandı: {loop}.", "shuffled": "Kuyruk karıştırıldı.", "no_previous_track": "Önceki parça yok.", "playing_previous": "Önceki parça çalıyor.", "previous_footer": "{displayName} tarafından önceki parça çalıyor.", "rewinded": "Müzik geri sarıldı", "rewind_footer": "{displayName} tarafından geri sarıldı.", "forward_limit": "Müzik uzunluğundan daha fazla ileriye saramazsınız.", "forwarded": "Müzik ileri sarıldı.", "forward_footer": "{displayName} tarafından ileri sarıldı.", "button_not_available": "<PERSON><PERSON> but<PERSON> kullanı<PERSON>az.", "no_music_playing": "Bu sunucuda çalan müzik yok."}}, "Evaluate code": "Kodu çalıştır.", "Leave a guild": "Bir sunucudan çık.", "List all guilds the bot is in": "Botun bulunduğu tüm sunucuları listele.", "Restart the bot": "<PERSON><PERSON> ba<PERSON>.", "The loop mode you want to set": "The loop mode you want to set"}