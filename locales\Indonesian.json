{"cmd": {"247": {"description": "Atur bot untuk tetap berada di voice channel", "errors": {"not_in_voice": "Anda perlu berada di voice channel untuk menggunakan command ini.", "generic": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mencoba menjalankan command ini."}, "messages": {"disabled": "`✅` | Mode 24/7 telah `DINONAKTIFKAN`", "enabled": "`✅` | Mode 24/7 telah `DIAKTIFKAN`. \n**Bot tidak akan meninggalkan voice channel meskipun tidak ada orang di voice channel.**"}}, "ping": {"description": "Menampilkan ping bot.", "content": "<PERSON><PERSON><PERSON>r ping...", "bot_latency": "<PERSON><PERSON><PERSON>", "api_latency": "Latensi API", "requested_by": "Direquest oleh {author}"}, "lavalink": {"description": "Menampilkan statistik Lavalink saat ini.", "title": "Statistik Lavalink", "content": "Player: {players}\nPlayer Aktif: {playingPlayers}\nUptime: {uptime}\nCore: {cores} Core\nMemory Usage: {used} / {reservable}\nSystem Load: {systemLoad}%\nLavalink Load: {lavalinkLoad}%"}, "invite": {"description": "Dapatkan link invite bot.", "content": "Anda dapat menginvite saya dengan mengklik tombol di bawah. Ada bug atau error? Bergabunglah ke server support!"}, "help": {"description": "Menampilkan Help menu.", "options": {"command": "command yang ingin Anda dapatkan informasinya"}, "content": "Halo! Aku adalah {bot}, bot musik yang dibuat dengan [Lavamusic](https://github.com/appujet/lavamusic) dan Discord. Anda dapat menggunakan `{prefix}help <command>` untuk mendapatkan informasi lebih lanjut tentang command.", "title": "<PERSON><PERSON>", "not_found": "command `{cmdName}` ini tidak ada.", "help_cmd": "**Deskripsi:** {description}\n**Penggunaan:** {usage}\n**Contoh:** {examples}\n**Alias:** {aliases}\n**<PERSON><PERSON><PERSON>:** {category}\n**Cooldown:** {cooldown} detik\n**Izin:** {premUser}\n**Izin Bot:** {premBot}\n**<PERSON><PERSON>ng:** {dev}\n**command Slash:** {slash}\n**Argumen:** {args}\n**Player:** {player}\n**DJ:** {dj}\n**Izin DJ:** {djPerm}\n**Suara:** {voice}", "footer": "<PERSON><PERSON><PERSON> {prefix}help <command> untuk informasi lebih lanjut tentang command"}, "botinfo": {"description": "Informasi tentang bot", "content": "Informasi Bot:\n- **Sistem Operasi**: {osInfo}\n- **Uptime**: {osUptime}\n- **Nama Host**: {osHostname}\n- **Arsitektur CPU**: {cpuInfo}\n- **Penggunaan CPU**: {cpuUsed}%\n- **Penggunaan Memori**: {memUsed}MB / {memTotal}GB\n- **Versi Node**: {nodeVersion}\n- **Versi Discord**: {discordJsVersion}\n- **Terhubung ke** {guilds} guild, {channels} saluran, dan {users} pengguna\n- **Total command**: {commands}"}, "about": {"description": "Menampilkan informasi tentang bot", "fields": {"creator": "Kreator", "repository": "Repositori", "support": "Support", "description": "Dia sangat ingin membuat proyek open source pertamanya untuk pengalaman coding yang lebih banyak. Dalam proyek ini, dia ditantang untuk membuat proyek dengan lebih sedikit bug. Semoga Anda menikmati menggunakan LavaMusic!"}}, "dj": {"description": "Mengelola mode DJ dan peran terkait", "errors": {"provide_role": "<PERSON><PERSON> berikan peran.", "no_roles": "<PERSON><PERSON> kosong.", "invalid_subcommand": "Harap berikan subcommand yang valid."}, "messages": {"role_exists": "Role DJ <@&{roleId}> sudah ditam<PERSON>.", "role_added": "Role DJ <@&{roleId}> telah di<PERSON>.", "role_not_found": "Role DJ <@&{roleId}> tidak ditambahkan.", "role_removed": "Role DJ <@&{roleId}> telah <PERSON>.", "all_roles_cleared": "<PERSON><PERSON><PERSON> DJ telah <PERSON>.", "toggle": "Mode DJ te<PERSON> {status}."}, "options": {"add": "Role DJ yang ingin <PERSON> ta<PERSON>kan", "remove": "Role DJ yang ingin <PERSON> hapus", "clear": "<PERSON><PERSON><PERSON><PERSON> semua role DJ", "toggle": "Mengaktifkan/menonaktifkan role DJ", "role": "Role DJ"}, "subcommands": "Subcommand"}, "language": {"description": "<PERSON><PERSON><PERSON> bahasa untuk bot", "invalid_language": "Harap berikan bahasa yang valid. Contoh: `EnglishUS` untuk Bahasa Inggris (Amerika Serikat)\n\nAnda dapat menemukan list bahasa yang di dukung [di sini](https://discord.com/developers/docs/reference#locales)\n\n**Bahasa yang Tersedia:**\n{languages}", "already_set": "Bahasa sudah diatur ke `{language}`", "not_set": "Bahasa belum diatur.", "set": "`✅` | Bahasa telah diatur ke `{language}`", "reset": "`✅` | Bahasa telah direset ke bahasa default.", "options": {"set": "<PERSON><PERSON><PERSON> bahasa untuk bot", "language": "Bahasa yang ingin Anda atur", "reset": "Mengu<PERSON> bahasa kembali ke bahasa default"}}, "prefix": {"description": "Menampilkan atau mengatur prefix bot", "errors": {"prefix_too_long": "Prefix tidak boleh lebih dari 3 karakter."}, "messages": {"current_prefix": "Prefix untuk server ini adalah `{prefix}`", "prefix_set": "Prefix untuk server ini sekarang adalah `{prefix}`", "prefix_reset": "Prefix untuk server ini sekarang adalah `{prefix}`"}, "options": {"set": "Mengatur prefix", "prefix": "Prefix yang ingin <PERSON>a atur", "reset": "Reset prefix ke default"}}, "setup": {"description": "<PERSON><PERSON><PERSON> bot", "errors": {"channel_exists": "Channel Song Request sudah ada.", "channel_not_exists": "Channel Song Request tidak ada.", "channel_delete_fail": "Channel Song Request telah dihapus. Jika channel tidak terhapus secara normal, mohon untuk menghapus sendiri."}, "messages": {"channel_created": "Channel Song Request telah dibuat di <#{channelId}>.", "channel_deleted": "Channel Song Request telah dihapus.", "channel_info": "Channel Song Request adalah <#{channelId}>."}, "options": {"create": "Membuat Channel Song Request", "delete": "Menghapus Channel Song Request", "info": "Menampilkan Channel Song Request"}}, "8d": {"description": "Mengaktifkan/menonaktifkan filter 8d", "messages": {"filter_enabled": "`✅` | Filter 8D telah `DIAKTIFKAN`.", "filter_disabled": "`✅` | Filter 8D telah `DINONAKTIFKAN`."}}, "bassboost": {"description": "Mengaktifkan/menonaktifkan filter bassboost", "messages": {"filter_enabled": "`✅` | Filter bassboost telah `DIAKTIFKAN`. \n**Hati-hati, mendengarkan terlalu keras dapat merusak pendengaran Anda!**", "filter_disabled": "`✅` | Filter bassboost telah `DINONAKTIFKAN`."}, "options": {"level": "The bassboost level you want to set"}}, "distortion": {"description": "Mengaktifkan/menonaktifkan filter distorsi", "messages": {"filter_enabled": "`✅` | Filter distorsi telah `DIAKTIFKAN`.", "filter_disabled": "`✅` | Filter distorsi telah `DINONAKTIFKAN`."}}, "karaoke": {"description": "Mengaktifkan/menonaktifkan filter karaoke", "messages": {"filter_enabled": "`✅` | Filter karaoke telah `DIAKTIFKAN`.", "filter_disabled": "`✅` | Filter karaoke telah `DINONAKTIFKAN`."}}, "lowpass": {"description": "Mengaktifkan/menonaktifkan filter lowpass", "messages": {"filter_enabled": "`✅` | Filter lowpass telah `DIAKTIFKAN`.", "filter_disabled": "`✅` | Filter lowpass telah `DINONAKTIFKAN`."}}, "nightcore": {"description": "Mengaktifkan/menonaktifkan filter nightcore", "messages": {"filter_enabled": "`✅` | Filter nightcore telah `DIAKTIFKAN`.", "filter_disabled": "`✅` | Filter nightcore telah `DINONAKTIFKAN`."}}, "pitch": {"description": "Mengaktifkan/menonaktifkan filter pitch", "options": {"pitch": "<PERSON><PERSON> yang ingin Anda atur untuk pitch (antara 0,5 dan 5)"}, "errors": {"invalid_number": "Harap berikan angka yang valid antara 0,5 dan 5."}, "messages": {"pitch_set": "`✅` | Pitch telah diatur ke **{pitch}**."}}, "rate": {"description": "Mengubah kecepatan lagu", "options": {"rate": "<PERSON><PERSON> yang ingin Anda atur untuk kecepatan (antara 0,5 dan 5)"}, "errors": {"invalid_number": "Harap berikan angka yang valid antara 0,5 dan 5."}, "messages": {"rate_set": "`✅` | Kecepatan telah diatur ke **{rate}**."}}, "reset": {"description": "Mengatur ulang filter aktif", "messages": {"filters_reset": "`✅` | Filter telah direset."}}, "rotation": {"description": "Mengaktifkan/menonaktifkan filter rotasi", "messages": {"enabled": "`✅` | Filter rotasi telah `DIAKTIFKAN`.", "disabled": "`✅` | Filter rotasi telah `DINONAKTIFKAN`."}}, "speed": {"description": "Mengubah kecepatan lagu", "options": {"speed": "<PERSON><PERSON><PERSON><PERSON> yang ingin <PERSON>a atur"}, "messages": {"invalid_number": "Harap berikan angka yang valid antara 0,5 dan 5.", "set_speed": "`✅` | Kecepatan telah diatur ke **{speed}**."}}, "tremolo": {"description": "Mengaktifkan/menonaktifkan filter tremolo", "messages": {"enabled": "`✅` | Filter tremolo telah `DIAKTIFKAN`.", "disabled": "`✅` | Filter tremolo telah `DINONAKTIFKAN`."}}, "vibrato": {"description": "Mengaktifkan/menonaktifkan filter vibrato", "messages": {"enabled": "`✅` | Filter vibrato telah `DIAKTIFKAN`.", "disabled": "`✅` | Filter vibrato telah `DINONAKTIFKAN`."}}, "autoplay": {"description": "Mengaktifkan/menonaktifkan pemutaran otomatis", "messages": {"enabled": "`✅` | Pemutaran otomatis telah `DIAKTIFKAN`.", "disabled": "`✅` | Pemutaran otomatis telah `DINONAKTIFKAN`."}}, "clearqueue": {"description": "<PERSON><PERSON><PERSON> ant<PERSON>n", "messages": {"cleared": "<PERSON><PERSON>an telah <PERSON>."}}, "grab": {"description": "Mengambil lagu yang sedang diputar ke DM Anda", "content": "**Durasi:** {length}\n**Direquest oleh:** <@{requester}>\n**Link:** [<PERSON><PERSON> di sini]({uri})", "check_dm": "<PERSON><PERSON><PERSON> perik<PERSON> DM Anda.", "dm_failed": "Saya tidak bisa mengirim DM kepada Anda. Pastikan allow direct messages diaktifkan."}, "join": {"description": "Join ke voice channel", "already_connected": "<PERSON>a sudah terhubung ke <#{channelId}>.", "no_voice_channel": "Anda perlu berada di voice channel untuk menggunakan command ini.", "joined": "Berhasil join ke <#{channelId}>."}, "leave": {"description": "Meninggalkan voice channel", "left": "<PERSON><PERSON><PERSON><PERSON> <#{channelId}>.", "not_in_channel": "Saya tidak berada di voice channel."}, "loop": {"description": "Mengulang lagu saat ini atau antrean", "looping_song": "**Men<PERSON><PERSON> lagu.**", "looping_queue": "**Mengulang antrean.**", "looping_off": "**Pengulangan sekarang dimatikan.**"}, "nowplaying": {"description": "Menampilkan lagu yang sedang diputar", "now_playing": "Sedang Diputar", "track_info": "[{title}]({uri}) - Direquest Oleh: <@{requester}>\n\n`{bar}`"}, "pause": {"description": "<PERSON><PERSON>da lagu saat ini", "successfully_paused": "<PERSON><PERSON><PERSON><PERSON> lagu."}, "play": {"description": "<PERSON><PERSON><PERSON> lagu dari YouTube, Spotify atau http", "options": {"song": "<PERSON>gu yang ingin Anda putar"}, "loading": "Memuat...", "errors": {"search_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat mencari.", "no_results": "<PERSON>idak di<PERSON>n hasil.", "queue_too_long": "Antrean terlalu panjang. Panjang maksimum adalah {maxQueueSize} lagu.", "playlist_too_long": "Playlist terlalu panjang. Panjang maksimum adalah {maxPlaylistSize} lagu."}, "added_to_queue": "<PERSON><PERSON><PERSON><PERSON> [{title}]({uri}) ke antrean.", "added_playlist_to_queue": "Menambahkan {length} lagu ke antrean."}, "playnext": {"description": "Menambahkan lagu untuk diputar selanjutnya dalam antrean", "options": {"song": "<PERSON>gu yang ingin Anda putar"}, "loading": "Memuat...", "errors": {"search_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat mencari.", "no_results": "<PERSON>idak di<PERSON>n hasil.", "queue_too_long": "Antrean terlalu panjang. Maksimum {maxQueueSize} lagu.", "playlist_too_long": "Playlist terlalu panjang. Maksimum {maxPlaylistSize} lagu."}, "added_to_play_next": "Menambahkan [{title}]({uri}) untuk diputar selanjutnya dalam antrean.", "added_playlist_to_play_next": "Menambahkan {length} lagu untuk diputar selanjutnya dalam antrean."}, "queue": {"description": "Menampilkan antrean saat ini", "now_playing": "Sedang diputar: [{title}]({uri}) - Direquest oleh: <@{requester}> - <PERSON><PERSON><PERSON>: `{duration}`", "live": "LANGSUNG", "track_info": "{index}. [{title}]({uri}) - Direquest oleh: <@{requester}> - <PERSON><PERSON><PERSON>: `{duration}`", "title": "<PERSON><PERSON><PERSON>", "page_info": "Halaman {index} dari {total}"}, "remove": {"description": "Mengh<PERSON><PERSON> lagu dari antrean", "options": {"song": "Nomor lagu yang ingin Anda hapus"}, "errors": {"no_songs": "Tidak ada lagu dalam antrean.", "invalid_number": "Harap berikan nomor lagu yang valid."}, "messages": {"removed": "Menghapus lagu nomor {songNumber} dari antrean."}}, "replay": {"description": "<PERSON><PERSON><PERSON> ulang lagu saat ini", "errors": {"not_seekable": "Tidak dapat memutar ulang lagu ini karena tidak dapat dicari."}, "messages": {"replaying": "<PERSON><PERSON>tar ulang lagu saat ini."}}, "resume": {"description": "Melanjutkan lagu saat ini", "errors": {"not_paused": "Player tidak sedang dijeda."}, "messages": {"resumed": "Melan<PERSON><PERSON><PERSON> lagu."}}, "search": {"description": "<PERSON><PERSON><PERSON> lagu", "options": {"song": "Lagu yang ingin Anda cari"}, "errors": {"no_results": "<PERSON>idak di<PERSON>n hasil.", "search_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat mencari."}, "messages": {"added_to_queue": "<PERSON><PERSON><PERSON><PERSON> [{title}]({uri}) ke antrean."}}, "seek": {"description": "<PERSON><PERSON>i ke waktu tertentu dalam lagu", "options": {"duration": "<PERSON><PERSON>i yang ingin dicari"}, "errors": {"invalid_format": "Format waktu tidak valid. Contoh: seek 1m, seek 1h 30m", "not_seekable": "Lagu ini tidak dapat dicari.", "beyond_duration": "Tidak dapat mencari melewati durasi lagu {length}."}, "messages": {"seeked_to": "<PERSON><PERSON><PERSON> ke {duration}"}}, "shuffle": {"description": "Mengacak antrean", "messages": {"shuffled": "Mengacak antrean."}}, "skip": {"description": "Melewati lagu saat ini", "messages": {"skipped": "<PERSON><PERSON><PERSON> [{title}]({uri})."}}, "skipto": {"description": "Melewati ke lagu tertentu dalam antrean", "options": {"number": "Nomor lagu yang ingin <PERSON>"}, "errors": {"invalid_number": "Harap berikan nomor yang valid."}, "messages": {"skipped_to": "<PERSON>ew<PERSON> ke lagu nomor {number}."}}, "stop": {"description": "Menghentikan musik dan <PERSON><PERSON> antrean", "messages": {"stopped": "Menghentikan musik dan members<PERSON>kan antrean."}}, "volume": {"description": "Mengatur volume musik", "options": {"number": "Volume yang ingin Anda atur"}, "messages": {"invalid_number": "Harap berikan nomor yang valid.", "too_low": "Volume tidak boleh lebih rendah dari 0.", "too_high": "Volume tidak boleh lebih tinggi dari 200. <PERSON><PERSON><PERSON><PERSON> Anda ingin merusak pendengaran atau speaker <PERSON><PERSON>? Hmm, saya rasa itu bukan ide yang bagus.", "set": "Mengatur volume ke {volume}"}}, "addsong": {"description": "<PERSON><PERSON><PERSON><PERSON> lagu ke playlist", "options": {"playlist": "Playlist yang ingin <PERSON> ta<PERSON>", "song": "<PERSON>gu yang ingin <PERSON>a tamba<PERSON>kan"}, "messages": {"no_playlist": "<PERSON><PERSON> berikan playlist", "no_song": "<PERSON><PERSON> berikan lagu", "playlist_not_found": "Playlist itu tidak ada", "no_songs_found": "Tidak ditemukan lagu", "added": "Men<PERSON>bah<PERSON> {count} lagu ke {playlist}"}}, "create": {"description": "Membuat playlist", "options": {"name": "<PERSON><PERSON> Playlist"}, "messages": {"name_too_long": "<PERSON><PERSON> playlist hanya boleh 50 karakter.", "playlist_exists": "Playlist dengan nama itu sudah ada. <PERSON><PERSON> gunakan nama yang berbeda.", "playlist_created": "Playlist **{name}** telah dibuat."}}, "delete": {"description": "<PERSON>gh<PERSON>us playlist", "options": {"playlist": "Playlist yang ingin <PERSON><PERSON> hapus"}, "messages": {"playlist_not_found": "Playlist itu tidak ada.", "playlist_deleted": "Menghapus playlist **{playlistName}**."}}, "list": {"description": "<PERSON><PERSON><PERSON> semua playlist untuk user", "options": {"user": "User yang playlistnya ingin <PERSON>a ambil"}, "messages": {"no_playlists": "User ini tidak memiliki Playlist.", "your": "<PERSON><PERSON>", "playlists_title": "Playlist {username}", "error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat mengambil Playlist."}}, "load": {"description": "Memuat playlist", "options": {"playlist": "Playlist yang ingin <PERSON> muat"}, "messages": {"playlist_not_exist": "Playlist itu tidak ada.", "playlist_empty": "Playlist itu kosong.", "playlist_loaded": "Memuat `{name}` dengan `{count}` lagu."}}, "removesong": {"description": "<PERSON><PERSON><PERSON><PERSON> lagu dari playlist", "options": {"playlist": "Playlist yang ingin <PERSON>a ha<PERSON> dari", "song": "<PERSON>gu yang ingin <PERSON>a hapus"}, "messages": {"provide_playlist": "<PERSON><PERSON> berikan playlist.", "provide_song": "<PERSON><PERSON> berikan lagu.", "playlist_not_exist": "Playlist itu tidak ada.", "song_not_found": "Tidak ditemukan lagu yang cocok.", "song_removed": "<PERSON>gh<PERSON><PERSON> {song} dari {playlist}.", "error_occurred": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat menghapus lagu."}}, "steal": {"description": "Mencuri playlist dari user lain dan men<PERSON><PERSON><PERSON><PERSON> ke playlist <PERSON><PERSON>", "options": {"playlist": "Playlist yang ingin Anda curi", "user": "User yang ingin Anda curi Playlistnya"}, "messages": {"provide_playlist": "<PERSON><PERSON> berikan nama playlist.", "provide_user": "Harap sebutkan user.", "playlist_not_exist": "Playlist itu tidak ada untuk user yang disebutkan.", "playlist_stolen": "<PERSON><PERSON><PERSON><PERSON> mencuri playlist `{playlist}` dari {user}.", "error_occurred": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat mencuri Playlist."}}, "lyrics": {"description": "Get's the lyrics of the currently playing track"}, "fairplay": {"description": "Set the bot to play music fairly"}, "playlocal": {"description": "Plays an your local audio files", "options": {"file": "The audio file you want to play"}}, "say": {"description": "Quick text to speech conversion", "options": {"text": "The text to say"}}, "speak": {"description": "Convert text to speech with advanced voice options", "options": {"text": "The text to convert to speech", "voice": "The voice to use for speech synthesis", "speed": "The speed of speech (0.5 to 2.0)"}}, "mluvit": {"description": "Czech text to speech conversion", "options": {"text": "Text to convert to Czech speech", "voice": "Czech voice to use (<PERSON><PERSON><PERSON> or <PERSON><PERSON>)", "speed": "Speech speed (0.5 to 2.0)"}}, "developer": {"description": "Shows information about the bot developer"}}, "buttons": {"invite": "Invite", "support": "Server Support", "previous": "Previous", "resume": "Resume", "stop": "Stop", "skip": "<PERSON><PERSON>", "loop": "Loop", "errors": {"not_author": "Anda tidak dapat menggunakan tombol ini."}}, "player": {"errors": {"no_player": "Tidak ada player yang aktif di server ini.", "no_channel": "Anda perlu berada di voice channel untuk menggunakan command ini.", "queue_empty": "<PERSON><PERSON>an kosong.", "no_previous": "Tidak ada lagu sebelumnya dalam antrean.", "no_song": "Tidak ada lagu dalam antrean.", "already_paused": "<PERSON><PERSON> sudah dijeda."}, "trackStart": {"now_playing": "Sedang diputar", "requested_by": "Direquest oleh {user}", "duration": "<PERSON><PERSON><PERSON>", "author": "Artis", "not_connected_to_voice_channel": "Anda tidak terhubung ke <#{channel}> untuk menggunakan tombol-tombol ini.", "need_dj_role": "<PERSON><PERSON> per<PERSON> memiliki role DJ un<PERSON><PERSON> mengg<PERSON>kan command ini.", "previous_by": "Sebelumnya oleh {user}", "no_previous_song": "Tidak ada lagu sebelumnya.", "paused_by": "Di pause oleh {user}", "resumed_by": "Di resume oleh {user}", "skipped_by": "Di skip oleh {user}", "no_more_songs_in_queue": "Tidak ada lagi lagu dalam antrean.", "looping_by": "Loop oleh {user}", "looping_queue_by": "Loop antrean oleh {user}", "looping_off_by": "Loop mati oleh {user}"}, "setupStart": {"now_playing": "Sedang diputar", "description": "[{title}]({uri}) oleh {author} • `[{length}]` - Direquest oleh <@{requester}>", "error_searching": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat mencari.", "no_results": "<PERSON>idak di<PERSON>n hasil.", "nothing_playing": "Tidak ada yang diputar saat ini.", "queue_too_long": "Antrean terlalu panjang. Maksimum {maxQueueSize} lagu.", "playlist_too_long": "Playlist terlalu panjang. Maksimum {maxPlaylistSize} lagu.", "added_to_queue": "<PERSON><PERSON><PERSON><PERSON> [{title}]({uri}) ke antrean.", "added_playlist_to_queue": "<PERSON><PERSON><PERSON><PERSON> [{length}] lagu dari playlist ke antrean."}}, "event": {"interaction": {"setup_channel": "Anda tidak dapat menggunakan command ini di setup channel.", "no_send_message": "<PERSON>a tidak memiliki izin **`SendMessage`** di `{guild}`\nChannel: {channel}.", "no_embed_links": "<PERSON>a tidak memiliki izin **`EmbedLinks`**.", "no_permission": "Saya tidak memiliki cukup izin untuk menjalankan command ini.", "no_user_permission": "Anda tidak memiliki cukup izin untuk menggunakan command ini.", "no_voice_channel": "Anda harus terhubung ke voice channel untuk menggunakan command `{command}` ini.", "no_connect_permission": "<PERSON>a tidak memiliki izin `CONNECT` untuk men<PERSON>lank<PERSON> command `{command}` ini.", "no_speak_permission": "<PERSON>a tidak memiliki izin `SPEAK` untuk menjalank<PERSON> command `{command}` ini.", "no_request_to_speak": "<PERSON>a tidak memiliki izin `REQUEST TO SPEAK` untuk menjalankan command `{command}` ini.", "different_voice_channel": "Anda tidak terhubung ke {channel} untuk menggunakan command `{command}` ini.", "no_music_playing": "Tidak ada lagu yang sedang diputar saat ini.", "no_dj_role": "Role DJ belum diatur.", "no_dj_permission": "<PERSON><PERSON> per<PERSON> memiliki role DJ un<PERSON><PERSON> mengg<PERSON>kan command ini.", "cooldown": "Harap tunggu {time} detik lagi sebelum menggunakan kembali command `{command}`.", "error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>: `{error}`"}, "message": {"prefix_mention": "Hai, prefix saya untuk server ini adalah `{prefix}`. Ingin informasi lebih lanjut? Lakukan `{prefix}help`\n<PERSON>tap <PERSON>, Tetap Luar Biasa!", "no_send_message": "<PERSON>a tidak memiliki izin **`SendMessage`** di `{guild}`\nChannel: {channel}.", "no_embed_links": "<PERSON>a tidak memiliki izin **`EmbedLinks`**.", "no_permission": "Saya tidak memiliki cukup izin untuk menjalankan command ini.", "no_user_permission": "Anda tidak memiliki cukup izin untuk menggunakan command ini.", "no_voice_channel": "Anda harus terhubung ke voice channel untuk menggunakan command `{command}` ini.", "no_connect_permission": "<PERSON>a tidak memiliki izin `CONNECT` untuk men<PERSON>lank<PERSON> command `{command}` ini.", "no_speak_permission": "<PERSON>a tidak memiliki izin `SPEAK` untuk menjalank<PERSON> command `{command}` ini.", "no_request_to_speak": "<PERSON>a tidak memiliki izin `REQUEST TO SPEAK` untuk menjalankan command `{command}` ini.", "different_voice_channel": "Anda tidak terhubung ke {channel} untuk menggunakan command `{command}` ini.", "no_music_playing": "Tidak ada lagu yang sedang diputar saat ini.", "no_dj_role": "Role DJ belum diatur.", "no_dj_permission": "<PERSON><PERSON> per<PERSON> memiliki role DJ un<PERSON><PERSON> mengg<PERSON>kan command ini.", "missing_arguments": "Argumen tidak lengkap", "missing_arguments_description": "Harap berikan argumen yang diperlukan untuk command `{command}`.\n\nContoh:\n{examples}", "syntax_footer": "Sintaks: [] = opsional, <> = wajib", "cooldown": "Harap tunggu {time} detik lagi sebelum menggunakan kembali command `{command}`.", "no_mention_everyone": "Anda tidak dapat menggunakan command ini dengan everyone atau here. <PERSON><PERSON><PERSON> gun<PERSON> slash command.", "error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>: `{error}`", "no_voice_channel_queue": "Anda tidak terhubung ke voice channel untuk menambahkan lagu ke antrean.", "no_permission_connect_speak": "Saya tidak memiliki cukup izin untuk terhubung/berbicara di <#{channel}>.", "different_voice_channel_queue": "Anda tidak terhubung ke <#{channel}> untuk menambahkan lagu ke antrean."}, "setupButton": {"no_voice_channel_button": "Anda tidak terhubung ke voice channel untuk menggunakan tombol ini.", "different_voice_channel_button": "Anda tidak terhubung ke {channel} untuk menggunakan tombol-tombol ini.", "now_playing": "Sedang diputar", "live": "LIVE", "requested_by": "Direquest oleh <@{requester}>", "no_dj_permission": "<PERSON><PERSON> per<PERSON> memiliki role DJ untu<PERSON> menggunakan tombol ini.", "volume_set": "Volume diatur ke {vol}%", "volume_footer": "Volume: {vol}%", "paused": "<PERSON><PERSON><PERSON>", "resumed": "<PERSON><PERSON><PERSON>", "pause_resume": "{name} lagu.", "pause_resume_footer": "{name} oleh {displayName}", "no_music_to_skip": "Tidak ada lagu untuk dilewati.", "skipped": "Melewati lagu.", "skipped_footer": "Dilewati oleh {displayName}", "stopped": "<PERSON>ghent<PERSON><PERSON> lagu.", "stopped_footer": "Dihentikan oleh {displayName}", "nothing_playing": "Tidak ada yang diputar saat ini.", "loop_set": "Loop diatur ke {loop}.", "loop_footer": "Loop diatur ke {loop} oleh {displayName}", "shuffled": "Mengacak antrean.", "no_previous_track": "Tidak ada lagu sebelumnya.", "playing_previous": "<PERSON><PERSON><PERSON> lagu sebelumnya.", "previous_footer": "Memutar lagu sebelumnya oleh {displayName}", "rewinded": "<PERSON><PERSON><PERSON> mundur lagu.", "rewind_footer": "Di<PERSON>ar mundur oleh {displayName}", "forward_limit": "Anda tidak dapat memajukan lagu lebih dari panjang lagu.", "forwarded": "Me<PERSON>ju<PERSON> lagu.", "forward_footer": "Dima<PERSON><PERSON> o<PERSON> {displayName}", "button_not_available": "Tombol ini tidak tersedia.", "no_music_playing": "Tidak ada lagu yang diputar di server ini."}}, "Evaluate code": "<PERSON><PERSON><PERSON> kode", "Leave a guild": "Tinggalkan server", "List all guilds the bot is in": "Daftar semua server tempat bot berada", "Restart the bot": "<PERSON><PERSON> bot", "The loop mode you want to set": "The loop mode you want to set"}